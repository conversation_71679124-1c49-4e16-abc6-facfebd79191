"use client";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { observer } from "mobx-react";
import React from "react";
import { StoreContext } from "../../store";
import { useLanguage } from "../../i18n/LanguageContext";

// 文字样式预设
const getTextResources = (t: (key: string) => string) => [
  // 标题类
  {
    name: t("text_title"),
    fontSize: 60,
    fontWeight: 700,
    fontFamily: "Roboto",
    category: "title",
    color: "#ffffff",
    description: t("text_desc_main_title"),
  },
  {
    name: t("text_subtitle"),
    fontSize: 36,
    fontWeight: 600,
    fontFamily: "Roboto",
    category: "title",
    color: "#ffffff",
    description: t("text_desc_subtitle"),
  },
  // 正文类
  {
    name: t("text_body"),
    fontSize: 24,
    fontWeight: 400,
    fontFamily: "Roboto",
    category: "body",
    color: "#ffffff",
    description: t("text_desc_body"),
  },
  {
    name: t("text_caption"),
    fontSize: 18,
    fontWeight: 300,
    fontFamily: "Roboto",
    category: "body",
    color: "#ffffff",
    description: t("text_desc_caption"),
  },
  // 创意字体类
  {
    name: "Creative Title",
    fontSize: 48,
    fontWeight: 700,
    fontFamily: "Bangers",
    category: "creative",
    color: "#ff6b35",
    description: t("text_desc_creative_title"),
  },
  {
    name: "Elegant Title",
    fontSize: 42,
    fontWeight: 400,
    fontFamily: "Berkshire Swash",
    category: "creative",
    color: "#8e44ad",
    description: t("text_desc_elegant_title"),
  },
  {
    name: "Tech Style",
    fontSize: 32,
    fontWeight: 500,
    fontFamily: "Anonymous Pro",
    category: "creative",
    color: "#00d4aa",
    description: t("text_desc_tech"),
  },
  {
    name: "Classic Serif",
    fontSize: 38,
    fontWeight: 400,
    fontFamily: "Cormorant Garamond",
    category: "creative",
    color: "#2c3e50",
    description: t("text_desc_classic_serif"),
  },
  // 特效样式类
  {
    name: "Neon Glow",
    fontSize: 44,
    fontWeight: 600,
    fontFamily: "Cabin",
    category: "effects",
    color: "#00ffff",
    description: "霓虹发光效果",
  },
  {
    name: "Retro Style",
    fontSize: 40,
    fontWeight: 700,
    fontFamily: "Francois One",
    category: "effects",
    color: "#ff4757",
    description: "复古风格",
  },
  {
    name: "Modern Clean",
    fontSize: 36,
    fontWeight: 300,
    fontFamily: "Encode Sans",
    category: "effects",
    color: "#2f3542",
    description: "现代简洁",
  },
  {
    name: "Playful Fun",
    fontSize: 46,
    fontWeight: 400,
    fontFamily: "Chelsea Market",
    category: "effects",
    color: "#ff9ff3",
    description: "趣味活泼",
  },
];

export const Texts = observer(() => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const TEXT_RESOURCES = getTextResources(t);
  const [searchTerm, setSearchTerm] = React.useState("");

  // 过滤和分组文字样式
  const groupedResources = React.useMemo(() => {
    const filteredResources = TEXT_RESOURCES.filter(
      (resource) =>
        resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const groups: { [key: string]: typeof TEXT_RESOURCES } = {};
    filteredResources.forEach((resource) => {
      if (!groups[resource.category]) {
        groups[resource.category] = [];
      }
      groups[resource.category].push(resource);
    });
    return groups;
  }, [TEXT_RESOURCES, searchTerm]);

  // 添加文字的处理函数
  const handleAddText = (resource: (typeof TEXT_RESOURCES)[0]) => {
    // 直接在addText中传递所有属性
    store.addText({
      text: resource.name,
      fontSize: resource.fontSize,
      fontWeight: resource.fontWeight,
      fontFamily: resource.fontFamily,
      fontColor: resource.color,
    });

    store.saveChange();
  };

  // 渲染文字样式按钮
  const renderTextButton = (resource: (typeof TEXT_RESOURCES)[0]) => {
    // 根据类别设置不同的背景渐变
    const getBackgroundGradient = (category: string) => {
      switch (category) {
        case "title":
          return "linear-gradient(135deg, #667eea 0%, #764ba2 100%)";
        case "body":
          return "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)";
        case "creative":
          return "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)";
        case "effects":
          return "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)";
        default:
          return "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)";
      }
    };

    const isLightColor = (color: string) => {
      const lightColors = ["#ffffff", "#00ffff", "#ff9ff3"];
      return lightColors.includes(color.toLowerCase());
    };

    return (
      <Button
        key={resource.name}
        onClick={() => handleAddText(resource)}
        variant="outlined"
        fullWidth
        sx={{
          textTransform: "none",
          minHeight: 70,
          p: 2,
          border: "1px solid",
          borderColor: "divider",
          background: "grey.100",
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
          justifyContent: "center",
          borderRadius: 2,
          overflow: "hidden",
          "&:hover": {
            transform: "translateY(-4px)",
            boxShadow: "0 8px 25px rgba(0,0,0,0.15)",
            borderColor: "primary.main",
          },
          "&:active": {
            transform: "translateY(-2px)",
          },
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
        }}
      >
        <Typography
          sx={{
            fontSize: Math.min(resource.fontSize * 0.35, 18),
            fontWeight: resource.fontWeight,
            fontFamily: resource.fontFamily,
            color: resource.color,
            lineHeight: 1.2,
            mb: 0.5,
            textAlign: "left",
            width: "100%",
            textShadow: isLightColor(resource.color)
              ? "0 0 2px #333, 0 0 2px #333, 0 0 2px #333, 0 0 2px #333"
              : "none",
          }}
        >
          {resource.name}
        </Typography>
        <Typography
          variant="caption"
          sx={{
            color: "text.secondary",
            fontSize: "0.7rem",
            textAlign: "left",
            width: "100%",
            fontWeight: 500,
          }}
        >
          {resource.description}
        </Typography>
      </Button>
    );
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 1,
        boxShadow: 1,
      }}
    >
      {/* 标题栏 */}
      <Box
        sx={{
          bgcolor: "grey.100",
          minHeight: 48,
          display: "flex",
          flexDirection: "column",
          px: 2,
          py: 1.5,
          flexShrink: 0,
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 1 }}>
          {t("Text")}
        </Typography>

        {/* 搜索框 */}
        <TextField
          size="small"
          placeholder={t("text_search_placeholder")}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={() => setSearchTerm("")}
                  edge="end"
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              bgcolor: "background.paper",
              "& fieldset": {
                borderColor: "divider",
              },
              "&:hover fieldset": {
                borderColor: "primary.main",
              },
            },
          }}
        />
      </Box>

      {/* 内容区域 */}
      <Box
        sx={{
          flex: 1,
          overflow: "auto",
          p: 2,
          "&::-webkit-scrollbar": {
            width: "8px",
          },
          "&::-webkit-scrollbar-track": {
            bgcolor: "grey.100",
          },
          "&::-webkit-scrollbar-thumb": {
            bgcolor: "grey.400",
            borderRadius: "4px",
          },
        }}
      >
        {Object.keys(groupedResources).length === 0 ? (
          // 空状态
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              py: 6,
              textAlign: "center",
            }}
          >
            <SearchIcon sx={{ fontSize: 48, color: "text.disabled", mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {t("text_no_results")}
            </Typography>
            <Typography variant="body2" color="text.disabled">
              {t("text_try_different_keywords")}
            </Typography>
          </Box>
        ) : (
          <Stack spacing={3}>
            {Object.entries(groupedResources).map(([category, resources]) => (
              <Box key={category}>
                {/* 类别标题 */}
                <Box sx={{ mb: 2 }}>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      color: "text.secondary",
                      fontWeight: 600,
                      textTransform: "uppercase",
                      letterSpacing: 1,
                      mb: 1,
                    }}
                  >
                    {category === "title" && t("text_category_title")}
                    {category === "body" && t("text_category_body")}
                    {category === "creative" && t("text_category_creative")}
                    {category === "effects" && t("text_category_effects")}
                  </Typography>
                  <Divider />
                </Box>

                {/* 文字样式网格 */}
                <Grid container spacing={1.5}>
                  {resources.map((resource) => (
                    <Grid item xs={12} sm={6} key={resource.name}>
                      {renderTextButton(resource)}
                    </Grid>
                  ))}
                </Grid>
              </Box>
            ))}
          </Stack>
        )}
      </Box>
    </Box>
  );
});
